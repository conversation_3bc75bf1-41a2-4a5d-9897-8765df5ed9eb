import { Inject, HTT<PERSON>ontroller, <PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPQuery } from '@eggjs/tegg';
import { HelloService } from '../../foo/index.ts';

@HTTPController({
  path: '/bar',
})
export class UserController {
  @Inject()
  private helloService: HelloService;

  @HTTPMethod({
    method: 'GET',
    path: 'user',
  })
  async user(@HTTPQuery({ name: 'userId' }) userId: string) {
    return await this.helloService.hello(userId);
  }
}

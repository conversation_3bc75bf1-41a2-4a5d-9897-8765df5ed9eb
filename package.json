{"name": "hackernews-tegg", "description": "Hello Egg.js", "private": true, "type": "module", "egg": {"typescript": true}, "scripts": {"start": "eggctl start --daemon --title=egg-server-hackernews-tegg", "stop": "eggctl stop --title=egg-server-hackernews-tegg", "dev": "egg-bin dev  --port 7001", "test:local": "cross-env NODE_OPTIONS=\"--import @oxc-node/core/register\" vitest run", "pretest": "npm run clean && npm run lint -- --fix", "test": "npm run test:local", "preci": "npm run clean && npm run lint", "ci": "npm run test:local -- --coverage", "postci": "npm run prepublishOnly && npm start && sleep 10 && npm stop && npm run clean", "lint": "oxlint --type-aware", "typecheck": "tsc --noEmit", "tsc": "tsc", "clean": "tsc -b --clean", "prepublishOnly": "npm run clean && npm run tsc"}, "dependencies": {"@eggjs/scripts": "beta", "@eggjs/tegg": "4.0.0-beta.30", "@eggjs/tracer": "beta", "egg": "beta"}, "devDependencies": {"@eggjs/bin": "beta", "@eggjs/mock": "beta", "@eggjs/tsconfig": "beta", "@oxc-node/core": "^0.0.32", "@types/node": "24", "@vitest/coverage-v8": "4", "cross-env": "10", "egg-bin": "7.0.0-beta.0", "oxlint": "1", "oxlint-tsgolint": "^0.2.0", "typescript": "5", "vitest": "4"}, "engines": {"node": ">=22.18.0"}, "homepage": "https://github.com/YOUR_USERNAME/YOUR_REPO#readme", "bugs": {"url": "https://github.com/YOUR_USERNAME/YOUR_REPO/issues"}, "repository": {"type": "git", "url": "git+https://github.com/YOUR_USERNAME/YOUR_REPO.git"}, "author": "Author Name <<EMAIL>>", "license": "MIT"}
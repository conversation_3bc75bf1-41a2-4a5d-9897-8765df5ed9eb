
{
  "extends": "@eggjs/tsconfig",
  "compilerOptions": {
    "target": "ES2022",
    "module": "CommonJS", // 由 NodeNext 改为 CommonJS，提升兼容性
    "moduleResolution": "node", // 与 CommonJS 匹配的解析策略
    "declaration": false,
    "baseUrl": ".",
    "paths": { // 添加路径映射，辅助模块解析
      "@eggjs/tegg": ["./node_modules/@eggjs/tegg"]
    },
    "esModuleInterop": true // 确保启用，以更好地处理 CommonJS/ES 模块的互操作[4,5](@ref)
  },
  "include": [ // 明确包含需要编译的目录
    "app/**/*",
    "config/**/*",
    "typings/**/*"
  ]
}